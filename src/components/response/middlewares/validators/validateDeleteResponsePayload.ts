import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { Var } from '../../../../global/var';

export const validateDeleteResponsePayload = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Validate that responseId exists and is not undefined
    if (!req.params.responseId || req.params.responseId === 'undefined') {
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} Response ID is required`,
      });
    }

    // Validate that responseId is a valid UUID format
    const uuidSchema = Joi.string().uuid().required();
    const { error: uuidError } = uuidSchema.validate(req.params.responseId);

    if (uuidError) {
      return res.status(400).json({
        success: false,
        message: `${Var.app.emoji.failure} Invalid response ID format`,
      });
    }

    res.locals.responseId = req.params.responseId;

    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error validating delete response payload:`, error);
    return res.status(500).json({
      success: false,
      message: `${Var.app.emoji.failure} Server error while validating request`,
    });
  }
};
