import { Parser } from 'json2csv';
import { fetchResponsesForExport, createExportSuccessResponse, createExportErrorResponse, ExportOptions, ResponseData } from './exportResponsesBase';

// SECURITY HARDENING: CSV injection protection
const sanitizeForCSV = (value: any): string => {
  if (value === null || value === undefined) return '';

  const stringValue = String(value);

  // Prevent CSV injection by escaping dangerous characters
  if (
    stringValue.startsWith('=') ||
    stringValue.startsWith('+') ||
    stringValue.startsWith('-') ||
    stringValue.startsWith('@') ||
    stringValue.startsWith('\t') ||
    stringValue.startsWith('\r')
  ) {
    return `'${stringValue}`;
  }

  return stringValue;
};

export const exportResponsesAsCsv = async (surveyId: string, accountId: string, includeDeleted: boolean = false, whereConditions?: any) => {
  try {
    const options: ExportOptions = {
      surveyId,
      accountId,
      includeDeleted,
      whereConditions,
    };

    const fetchResult = await fetchResponsesForExport(options);

    if (!fetchResult.success) {
      return createExportErrorResponse('csv', fetchResult.data);
    }

    const responses = fetchResult.data;

    // Type guard to ensure responses is not null
    if (!responses) {
      return createExportErrorResponse('csv', 'No response data available');
    }

    // SECURITY HARDENING: Apply CSV sanitization to all exported data
    const processedResponses = responses.map((response: ResponseData) => {
      const flatResponse: any = {
        id: sanitizeForCSV(response.id),
        created_at: sanitizeForCSV(response.created_at),
        is_deleted: sanitizeForCSV(response.is_deleted),
      };

      Object.entries(response.response_data || {}).forEach(([key, value]) => {
        const sanitizedValue = typeof value === 'object' ? JSON.stringify(value) : value;
        flatResponse[`response_${key}`] = sanitizeForCSV(sanitizedValue);
      });

      Object.entries(response.respondent_details || {}).forEach(([key, value]) => {
        const sanitizedValue = typeof value === 'object' ? JSON.stringify(value) : value;
        flatResponse[`respondent_${key}`] = sanitizeForCSV(sanitizedValue);
      });

      return flatResponse;
    });

    const fields = Object.keys(processedResponses[0]);
    const opts = { fields };
    const parser = new Parser(opts);
    const csv = parser.parse(processedResponses);

    return createExportSuccessResponse(surveyId, 'csv', csv);
  } catch (error) {
    return createExportErrorResponse('csv', error);
  }
};
