import { DataTypes, ModelAttributes, ModelOptions } from 'sequelize';
import { Sequelize } from '../../../global/var';

const modelName: string = 'response';

const modelAttributes: ModelAttributes = {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    allowNull: false,
    unique: true,
    primaryKey: true,
  },
  survey_public_key: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'surveys',
      key: 'public_key',
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  },
  survey_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'surveys',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  },
  account_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'accounts',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  },
  response_data: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: {},
  },
  respondent_details: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
  },
  meta: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
  },
  is_deleted: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
};

const modelOptions: ModelOptions = {
  tableName: 'responses',
  timestamps: true, // Enable timestamps to use created_at and updated_at columns
  underscored: true, // Use snake_case for column names
  freezeTableName: true,
  indexes: [
    {
      fields: ['survey_public_key'],
      name: 'responses_public_key_idx',
    },
    {
      fields: ['is_deleted'],
      name: 'responses_is_deleted_idx',
    },
    {
      fields: ['survey_id', 'is_deleted'],
      name: 'responses_survey_id_is_deleted_idx',
    },
    {
      fields: ['account_id'],
      name: 'responses_account_id_idx',
    },
    {
      fields: ['survey_id', 'account_id'],
      name: 'responses_survey_id_account_id_idx',
    },
  ],
};

export const responseModel = Sequelize.define(modelName, modelAttributes, modelOptions);
