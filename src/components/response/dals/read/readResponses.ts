import { responseModel } from '../../models';
import { logger } from '../../../../global/services';
import { Var } from '../../../../global/var';

interface ResponseFilters {
  timeFilter?: string;
  customStartDate?: string;
  customEndDate?: string;
  page?: number;
  limit?: number;
}

/**
 * Pure DAL function to fetch responses based on pre-computed where conditions
 * This function only retrieves data and does not contain any business logic
 *
 * @param whereConditions - Pre-computed Sequelize where conditions
 * @param page - Page number for pagination
 * @param limit - Number of items per page
 * @returns Promise with responses and total count
 */
export const fetchResponsesWithConditions = async (whereConditions: any, page: number = 1, limit: number = 10) => {
  try {
    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await responseModel.count({
      where: whereConditions,
    });

    // Fetch responses with pagination
    const responseData = await responseModel.findAll({
      where: whereConditions,
      order: [['created_at', 'DESC']], // Newest first
      limit,
      offset,
      attributes: ['id', 'response_data', 'respondent_details', 'meta', 'created_at', 'is_deleted'],
    });

    // Transform responses to array format
    const responses: any[] = responseData.map(responseData => {
      // Clean meta data - remove sensitive information but keep region
      const originalMeta = responseData.dataValues.meta || {};
      const { ip, ...cleanMeta } = {
        ...originalMeta,
        // Keep region/country for analytics but remove IP
        region: originalMeta.country || originalMeta.region,
        // Keep userAgent for debugging and analytics purposes
      };

      return {
        // Include response ID for survey owners to enable deletion functionality
        id: responseData.dataValues.id,
        response_data: responseData.dataValues.response_data,
        respondent_details: responseData.dataValues.respondent_details,
        meta: cleanMeta,
        created_at: responseData.dataValues.created_at,
        is_deleted: responseData.dataValues.is_deleted,
      };
    });

    return {
      success: true,
      message: `${Var.app.emoji.success} Responses retrieved`,
      payload: {
        responses,
        totalCount,
      },
    };
  } catch (error) {
    logger.error('Error fetching responses with conditions:', error);
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not retrieve responses`,
      payload: error,
    };
  }
};

/**
 * Pure DAL function to fetch raw response data for analytics calculations
 * This function only retrieves data needed for analytics and does not perform any calculations
 *
 * @param whereConditions - Pre-computed Sequelize where conditions for current period
 * @param previousPeriodConditions - Pre-computed Sequelize where conditions for previous period (optional)
 * @returns Promise with raw response data for analytics
 */
export const fetchAnalyticsData = async (whereConditions: any, previousPeriodConditions?: any) => {
  try {
    // Get all responses for current period analytics
    const currentPeriodResponses = await responseModel.findAll({
      where: whereConditions,
      attributes: ['created_at', 'meta'],
      order: [['created_at', 'ASC']],
    });

    let previousPeriodResponses: any[] = [];

    // Get previous period responses if conditions provided
    if (previousPeriodConditions) {
      previousPeriodResponses = await responseModel.findAll({
        where: previousPeriodConditions,
        attributes: ['created_at'],
      });
    }

    return {
      success: true,
      payload: {
        currentPeriodResponses: currentPeriodResponses.map(r => r.dataValues),
        previousPeriodResponses: previousPeriodResponses.map(r => r.dataValues),
      },
    };
  } catch (error) {
    logger.error('Error fetching analytics data:', error);
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not retrieve analytics data`,
      payload: error,
    };
  }
};

/**
 * DEPRECATED: Legacy function for backward compatibility
 * This function now uses the new refactored approach with separated concerns
 *
 * @deprecated Use fetchResponsesWithConditions and fetchAnalyticsData with processTimeFilter instead
 * @param surveyId - The unique UUID of the survey whose responses to retrieve
 * @param accountId - The unique UUID of the account that owns the survey
 * @param filters - Optional filters for responses
 * @returns Promise<{success: boolean, message: string, payload: any}> - Operation result with responses and analytics
 */
export const readResponses: any = async (surveyId: string, accountId: string, filters: ResponseFilters = {}) => {
  try {
    const { timeFilter, customStartDate, customEndDate, page = 1, limit = 10 } = filters;

    // Import helper functions (dynamic import to avoid circular dependencies)
    const { processTimeFilter, generateAnalyticsFromData } = await import('../../helpers/analyticsHelpers');

    // Process time filter and generate where conditions
    const { whereConditions, previousPeriodConditions, trendLabel, headerText } = processTimeFilter(surveyId, accountId, timeFilter, customStartDate, customEndDate);

    // Fetch responses with pagination
    const responsesResult = await fetchResponsesWithConditions(whereConditions, page, limit);

    if (!responsesResult.success) {
      return responsesResult;
    }

    // Fetch analytics data
    const analyticsResult = await fetchAnalyticsData(whereConditions, previousPeriodConditions);

    if (!analyticsResult.success) {
      return analyticsResult;
    }

    // Generate analytics from raw data
    const analyticsData = analyticsResult.payload as any;
    const analytics = generateAnalyticsFromData(analyticsData.currentPeriodResponses, analyticsData.previousPeriodResponses, trendLabel, timeFilter, headerText);

    // Combine responses and analytics (maintaining backward compatibility)
    const responsesData = responsesResult.payload as any;
    const finalPayload = {
      ...responsesData,
      avgCompletionTime: analytics.avgCompletionTime,
      avgResponsesPerDay: analytics.avgResponsesPerDay,
      oldestResponseTimestamp: analytics.oldestResponseTimestamp,
      responsesByDay: analytics.responsesByDay,
      responseRateTrendComponents: analytics.responseRateTrendComponents,
      headerText: analytics.headerText,
    };

    return {
      success: true,
      message: responsesResult.message,
      payload: finalPayload,
    };
  } catch (error) {
    // Log error for debugging and monitoring
    logger.error('Error retrieving responses:', error);
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not retrieve responses`,
      payload: error,
    };
  }
};
