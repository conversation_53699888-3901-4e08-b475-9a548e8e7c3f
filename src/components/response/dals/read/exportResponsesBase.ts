import { responseModel } from '../../../response/models';
import { Var } from '../../../../global/var';

/**
 * Base export function that handles common logic for all export formats
 * This eliminates redundancy across CSV, JSON, and PDF export functions
 */

export interface ExportOptions {
  surveyId: string;
  accountId: string;
  includeDeleted?: boolean;
  whereConditions?: any;
}

export interface ResponseData {
  id: string;
  survey_id: string;
  account_id: string;
  response_data: any;
  respondent_details: any;
  meta: any;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
}

export interface ExportResult {
  success: boolean;
  message: string;
  payload: {
    filename: string;
    data: any;
  } | null;
}

export interface FetchExportResult {
  success: boolean;
  message: string;
  data: ResponseData[] | null;
}

/**
 * Builds the final where conditions for database query
 * Consolidates the logic that was duplicated across all export functions
 */
export const buildExportWhereConditions = (options: ExportOptions): any => {
  const { surveyId, accountId, includeDeleted = false, whereConditions } = options;

  let finalWhereConditions: any;

  if (whereConditions) {
    // Use provided where conditions (which include time filtering)
    finalWhereConditions = { ...whereConditions };
    if (!includeDeleted) {
      finalWhereConditions.is_deleted = false;
    }
  } else {
    // Fallback to basic conditions
    finalWhereConditions = {
      survey_id: surveyId,
      account_id: accountId,
    };
    if (!includeDeleted) {
      finalWhereConditions.is_deleted = false;
    }
  }

  return finalWhereConditions;
};

/**
 * Fetches responses for export with common query logic
 * Eliminates duplicate database query patterns
 */
export const fetchResponsesForExport = async (options: ExportOptions): Promise<FetchExportResult> => {
  try {
    const finalWhereConditions = buildExportWhereConditions(options);

    const responses = await responseModel.findAll({
      where: finalWhereConditions,
      order: [['created_at', 'DESC']],
    });

    if (responses.length === 0) {
      return {
        success: false,
        message: `${Var.app.emoji.warning} No responses found for this survey`,
        data: null,
      };
    }

    return {
      success: true,
      message: 'Responses fetched successfully',
      data: responses.map(response => response.dataValues) as ResponseData[],
    };
  } catch (error) {
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not fetch responses for export`,
      data: null,
    };
  }
};

/**
 * Generates standardized filename for exports
 */
export const generateExportFilename = (surveyId: string, format: string): string => {
  const timestamp = new Date().toISOString();
  return `responses_${surveyId}_${timestamp}.${format}`;
};

/**
 * Creates standardized export metadata
 */
export const createExportMetadata = (surveyId: string, totalResponses: number, includeDeleted: boolean, format: string) => {
  return {
    survey_id: surveyId,
    total_responses: totalResponses,
    exported_at: new Date().toISOString(),
    include_deleted: includeDeleted,
    format,
  };
};

/**
 * Standardized success response for exports
 */
export const createExportSuccessResponse = (surveyId: string, format: string, data: any): ExportResult => {
  return {
    success: true,
    message: `${Var.app.emoji.success} Responses exported`,
    payload: {
      filename: generateExportFilename(surveyId, format),
      data,
    },
  };
};

/**
 * Standardized error response for exports
 */
export const createExportErrorResponse = (format: string, error?: any): ExportResult => {
  return {
    success: false,
    message: `${Var.app.emoji.failure} Could not export responses as ${format.toUpperCase()}`,
    payload: error || null,
  };
};
